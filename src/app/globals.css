@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --contrast: #FF4D45;

  --device-width: 1920;
	--device-height: 650;
  
  --gap: 20px;
  --safe: 40px;
  --columns: 12;

}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-contrast: var(--contrast);

  --font-sans: var(--font-sans);

  --ease-*: initial;
  --ease-in-quad: cubic-bezier(0.55, 0.085, 0.68, 0.53);
	--ease-in-cubic: cubic-bezier(0.55, 0.055, 0.675, 0.19);
	--ease-in-quart: cubic-bezier(0.895, 0.03, 0.685, 0.22);
	--ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
	--ease-in-expo: cubic-bezier(0.95, 0.05, 0.795, 0.035);
	--ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.335);
	--ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
	--ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
	--ease-out-quart: cubic-bezier(0.165, 0.84, 0.44, 1);
	--ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
	--ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
	--ease-out-circ: cubic-bezier(0.075, 0.82, 0.165, 1);
	--ease-in-out-quad: cubic-bezier(0.455, 0.03, 0.515, 0.955);
	--ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
	--ease-in-out-quart: cubic-bezier(0.77, 0, 0.175, 1);
	--ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);
	--ease-in-out-expo: cubic-bezier(1, 0, 0, 1);
	--ease-in-out-circ: cubic-bezier(0.785, 0.135, 0.15, 0.86);
	--ease-gleasing: cubic-bezier(0.4, 0, 0, 1);

  --width-columns-2: calc((2 * var(--column-width)) + (1 * var(--gap)));
  --width-columns-3: calc((3 * var(--column-width)) + (2 * var(--gap)));
  --width-columns-4: calc((4 * var(--column-width)) + (3 * var(--gap)));
  --width-columns-5: calc((5 * var(--column-width)) + (4 * var(--gap)));
  --width-columns-6: calc((6 * var(--column-width)) + (5 * var(--gap)));
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
}

@utility puka-grid {
	display: grid;
	grid-template-columns: repeat(var(--columns), 1fr);
	gap: var(--gap);
}

@utility puka-layout-block {
	margin-inline: auto;
	width: calc(100% - 2 * var(--safe));
}

@utility puka-layout-block-inner {
	padding-inline: var(--safe);
	width: 100%;
}

@utility puka-layout-grid {
	@apply puka-layout-block puka-grid;
}

@utility puka-layout-grid-inner {
	@apply puka-layout-block-inner puka-grid;
}


.word {
  overflow: hidden;
}

.variant--default {
  position: relative;
  display: inline-flex;
  font-weight: 600;
  align-items: center;
  gap: 0.25em;
  cursor: pointer;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    width: 100%;
    background: var(--color-contrast);
    transform: scaleX(1);
    transform-origin: left;
  }

  &:hover::after {
    animation: underline-disappear-reappear 0.6s var(--ease-in-out-expo) forwards;
  }
}

@keyframes underline-disappear-reappear {
  0% {
    transform: scaleX(1);
    transform-origin: right;
  }
  49.999% {
    transform: scaleX(0);
    transform-origin: right;
  }
  50% {
    transform: scaleX(0);
    transform-origin: left;
  }
  100% {
    transform: scaleX(1);
    transform-origin: left;
  }
}
