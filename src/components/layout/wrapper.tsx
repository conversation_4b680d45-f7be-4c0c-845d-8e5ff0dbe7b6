"use client";

import cn from "clsx";
import type { LenisOptions } from "lenis";
import { Lenis } from "./lenis";

interface WrapperProps extends React.HTMLAttributes<HTMLDivElement> {
	lenis?: boolean | LenisOptions;
}

export function Wrapper({
	children,
	className,
	lenis = true,
	...props
}: WrapperProps) {
	return (
		<>
			<main
				className={cn(
					"relative flex grow flex-col !space-y-16 lg:!space-y-32",
					className,
				)}
				{...props}
			>
				<div className="revealer | min-h-[100svh] pointer-events-none fixed inset-0 z-[60] origin-top bg-contrast grid place-items-center">
					<img
						src="/punk_filled.svg"
						alt=""
						className="punk-head | max-lg:w-[50vw] rotate-[-14deg]"
					/>
				</div>
				{children}
			</main>
			{lenis && <Lenis root options={typeof lenis === "object" ? lenis : {}} />}
		</>
	);
}
