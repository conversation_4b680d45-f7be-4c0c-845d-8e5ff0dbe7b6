"use client";

import type { FC } from "react";
import gsap from "gsap";
import { SplitText } from "gsap/SplitText";
import { useGSAP } from "@gsap/react";

gsap.registerPlugin(SplitText);

export const Hero: FC = () => {
	useGSAP(() => {
		const targets = gsap.utils.toArray<HTMLElement>(".h1-big [data-split]");

		const splits = targets.map(
			(el) =>
				new SplitText(el, {
					type: "words",
					wordsClass: "word",
					mask: "words",
				}),
		);

		const words: HTMLElement[] = [];
		for (const s of splits) {
			words.push(...(s.words as HTMLElement[]));
		}

		const tl = gsap.timeline();
		tl.from(words, {
			yPercent: 100,
			duration: 1,
			delay: 0.5,
			stagger: 0.08,
			ease: "power3.out",
		})
			.from(
				words,
				{
					opacity: 0,
					duration: 1,
					stagger: 0.08,
					ease: "power3.out",
				},
				"<",
			)
			.from("[data-after]", {
				opacity: 0,
				duration: 1,
				ease: "power3.out",
			});

		return () => {
			for (const s of splits) {
				s.revert();
			}
		};
	});

	return (
		<section className="h-[100svh] flex items-end relative">
			<div className="absolute inset-0">
				<img src="/rb.svg" alt="" className="w-full h-auto" />
			</div>
			<div className="puka-layout-block pb-10 relative z-10">
				<div className="h1-big | text-[9.72vw] font-black uppercase leading-[80%] text-center">
					<span data-split className="inline-block">
						Der erste
					</span>

					<span className="flex items-start justify-center">
						<span data-split>Schritt</span>

						<span
							data-after
							className="text-[0.6vw] leading-none aspect-square block min-h-[7.4vw] text-left mx-6 pt-2.5"
						>
							Entdecke die Welt <br />
							der Gedanken
						</span>

						<span data-split>zum</span>
					</span>

					<span data-split>Podcast-Erfolg</span>
				</div>
			</div>
		</section>
	);
};
