"use client";

import type { FC } from "react";

const services = [
	{
		id: 1,
		title: "Ren<PERSON>",
		description:
			"Entfalte dein Podcast-Potenzial mit unserem mietbaren puka.studio!",
	},
	{
		id: 2,
		title: "Cast",
		description:
			"Wir übernehmen alles, von der Aufnahme bis zur Veröffentlichung, damit du dich auf deinen Content konzentrieren kannst.",
	},
	{
		id: 3,
		title: "Cut",
		description: "<PERSON><PERSON> zu perfektem Podcast-Schnitt und Mastering!",
	},
	{
		id: 4,
		title: "Design",
		description: "",
	},
	{
		id: 5,
		title: "Development",
		description: "",
	},
];

export const Services: FC = () => {
	return (
		<section className="relative">
			<div className="puka-layout-block">
				{services.map((service, index) => (
					<div
						className="sticky py-10 puka-grid border-t border-neutral-200 bg-background"
						key={`service-${service.id}`}
						style={{ top: `${96 * index}px` }}
					>
						<div className="col-span-3">
							<h3 className="text-xs uppercase font-bold">
								Service {service.id}
							</h3>
						</div>
						<div className="col-span-6 space-y-12">
							<h2 className="text-[2.72vw] leading-[80%] font-black uppercase">
								{service.title}
							</h2>
							<p className="text-[1vw] text-balance max-w-md">
								{service.description}
							</p>
							<button
								className="variant--default | text-base uppercase"
								type="button"
							>
								Mehr zu {service.title}
							</button>
						</div>
						<div className="col-span-3">
							<div className="w-full aspect-[3/4] bg-neutral-100 rounded-md grid place-items-center">
								<img
									src={`/${service.title}.svg`}
									alt={service.title}
									className="aspect-square w-1/2 object-contain"
								/>
							</div>
						</div>
					</div>
				))}
			</div>
		</section>
	);
};
